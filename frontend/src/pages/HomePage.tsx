import React from 'react';
import { Link } from 'react-router-dom';
import { MessageSquare, BarChart3, Shield, Clock } from 'lucide-react';
import Button from '../components/UI/Button';
import Card from '../components/UI/Card';

const HomePage: React.FC = () => {
  const features = [
    {
      icon: <MessageSquare className="w-8 h-8 text-orange-500" />,
      title: 'Submit Feedback',
      description: 'Share your concerns, suggestions, and ideas with government officials through our secure platform.'
    },
    {
      icon: <BarChart3 className="w-8 h-8 text-orange-500" />,
      title: 'Track Progress',
      description: 'Monitor the status of your submissions and see how your feedback is being addressed.'
    },
    {
      icon: <Shield className="w-8 h-8 text-orange-500" />,
      title: 'Secure & Anonymous',
      description: 'Your privacy is protected. Submit feedback anonymously or with your identity, your choice.'
    },
    {
      icon: <Clock className="w-8 h-8 text-orange-500" />,
      title: 'Real-time Updates',
      description: 'Get notified when there are updates on your submissions or related topics in your area.'
    }
  ];

  const stats = [
    { value: '15,247', label: 'Feedback Submitted' },
    { value: '12,891', label: 'Issues Resolved' },
    { value: '89%', label: 'Satisfaction Rate' },
    { value: '24h', label: 'Avg Response Time' }
  ];

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>

        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-20 w-64 h-64 bg-orange-500/20 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-20 w-96 h-96 bg-orange-600/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative container mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
          {/* New AI Enhancement Badge */}
          <div className="flex justify-center mb-8">
            <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-full px-4 py-2">
              <span className="text-orange-400 text-sm font-medium">New AI Enhancement</span>
            </div>
          </div>

          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
              Ultimate Protection for
              <span className="block text-white">Your Digital World</span>
            </h1>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              Stay safe from viruses, malware, and cyber threats with our cutting-edge antivirus solution.
              Secure your devices with real-time protection and advanced security features.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
              <Button size="lg" className="bg-orange-600 hover:bg-orange-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 px-8 py-4">
                Get the AntiVirus
              </Button>
            </div>

            {/* Trusted by Companies */}
            <div className="text-center">
              <p className="text-gray-400 text-sm mb-8">Trusted by World's Companies</p>
              <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
                <div className="flex items-center space-x-2 text-gray-500">
                  <div className="w-8 h-8 bg-gray-600 rounded-full"></div>
                  <span className="text-lg font-medium">logoipsum</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-500">
                  <div className="w-8 h-8 bg-gray-600 rounded-full"></div>
                  <span className="text-lg font-medium">Logoipsum</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-500">
                  <div className="w-8 h-8 bg-gray-600 rounded-full"></div>
                  <span className="text-lg font-medium">Logoipsum</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-500">
                  <div className="w-8 h-8 bg-gray-600 rounded-full"></div>
                  <span className="text-lg font-medium">logoipsum</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-500">
                  <div className="w-8 h-8 bg-gray-600 rounded-full"></div>
                  <span className="text-lg font-medium">logoipsum</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl sm:text-4xl font-bold text-white mb-2">
                  {stat.value}
                </div>
                <div className="text-gray-400 text-sm sm:text-base">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-900">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Advanced Security Features
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Comprehensive protection powered by cutting-edge technology and AI-driven threat detection
              to keep your digital life secure.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} hover className="text-center group bg-gray-800 border-gray-700">
                <div className="mb-4 flex justify-center">
                  <div className="p-3 rounded-full bg-gray-700 group-hover:scale-110 transition-transform duration-200">
                    {feature.icon}
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-400 leading-relaxed">
                  {feature.description}
                </p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-orange-600 to-orange-700 text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Secure Your Digital Life?
            </h2>
            <p className="text-xl text-orange-100 mb-8 max-w-2xl mx-auto">
              Join millions of users worldwide who trust our advanced antivirus solution
              to protect their devices and data.
            </p>
            <Button size="lg" className="bg-white text-orange-600 hover:bg-orange-50 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200">
              Download Free Trial
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;