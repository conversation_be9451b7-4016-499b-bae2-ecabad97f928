import React from 'react';
import { Link } from 'react-router-dom';
import { MessageSquare, BarChart3, Shield, Clock } from 'lucide-react';
import Button from '../components/UI/Button';
import Card from '../components/UI/Card';

const HomePage: React.FC = () => {
  const features = [
    {
      icon: <MessageSquare className="w-8 h-8 text-blue-600" />,
      title: 'Submit Feedback',
      description: 'Share your concerns, suggestions, and ideas with government officials through our secure platform.'
    },
    {
      icon: <BarChart3 className="w-8 h-8 text-blue-600" />,
      title: 'Track Progress',
      description: 'Monitor the status of your submissions and see how your feedback is being addressed.'
    },
    {
      icon: <Shield className="w-8 h-8 text-blue-600" />,
      title: 'Secure & Anonymous',
      description: 'Your privacy is protected. Submit feedback anonymously or with your identity, your choice.'
    },
    {
      icon: <Clock className="w-8 h-8 text-blue-600" />,
      title: 'Real-time Updates',
      description: 'Get notified when there are updates on your submissions or related topics in your area.'
    }
  ];

  const supportFeatures = [
    {
      icon: <MessageSquare className="w-12 h-12 text-blue-600" />,
      title: 'Free support',
      description: 'It is a long established fact that a reader.'
    },
    {
      icon: <BarChart3 className="w-12 h-12 text-blue-600" />,
      title: 'Online support',
      description: 'It is a long established fact that a reader.'
    },
    {
      icon: <Shield className="w-12 h-12 text-blue-600" />,
      title: 'Chamber service',
      description: 'It is a long established fact that a reader.'
    }
  ];

  const stats = [
    { value: '15,247', label: 'Feedback Submitted' },
    { value: '12,891', label: 'Issues Resolved' },
    { value: '89%', label: 'Satisfaction Rate' },
    { value: '24h', label: 'Avg Response Time' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="relative bg-gray-50 overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="max-w-xl">
              {/* Welcome Badge */}
              <div className="inline-flex items-center bg-white border border-gray-200 rounded-full px-4 py-2 mb-6">
                <span className="text-sm text-gray-600">Welcome to civic engagement</span>
              </div>

              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                Civic feedback that
                <span className="block">changes lives.</span>
              </h1>

              <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                It is a long established fact that a reader will distracted by the readable content of a page when looking.
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/feedback">
                  <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-medium transition-colors duration-200">
                    Start Free Trial
                  </Button>
                </Link>
                <Button variant="outline" size="lg" className="border-gray-300 text-gray-700 hover:bg-gray-50 px-8 py-4 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2">
                  <span>Watch The Demo</span>
                  <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M8 5v10l8-5-8-5z"/>
                    </svg>
                  </div>
                </Button>
              </div>
            </div>

            {/* Right Content - Image/Illustration */}
            <div className="relative">
              <div className="bg-white rounded-3xl p-8 shadow-xl">
                <div className="text-center mb-6">
                  <div className="w-24 h-24 bg-blue-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <MessageSquare className="w-12 h-12 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Meet with our officials</h3>
                  <div className="flex justify-center space-x-2 mb-4">
                    <div className="w-8 h-8 bg-blue-600 rounded-full"></div>
                    <div className="w-8 h-8 bg-green-500 rounded-full"></div>
                    <div className="w-8 h-8 bg-orange-500 rounded-full"></div>
                    <div className="w-8 h-8 bg-purple-500 rounded-full"></div>
                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs font-bold">+</div>
                  </div>
                </div>

                {/* Review Card */}
                <div className="bg-gray-50 rounded-xl p-4 mb-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">A</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-sm font-medium text-gray-900">Alex Jordan</span>
                        <span className="text-xs text-gray-500">Citizen</span>
                      </div>
                      <p className="text-sm text-gray-600">Review: New best review</p>
                      <span className="text-xs text-gray-400">30 min ago</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                <BarChart3 className="w-8 h-8 text-blue-600" />
              </div>
              <div className="absolute -bottom-4 -left-4 w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                <Shield className="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Support Features Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {supportFeatures.map((feature, index) => (
              <div key={index} className="bg-gray-50 rounded-xl p-8 text-center border border-gray-100">
                <div className="mb-4 flex justify-center">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              Empowering Citizens Through Technology
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our platform bridges the gap between citizens and government, making it easier
              than ever to participate in the democratic process.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} hover className="text-center group bg-white border-gray-200">
                <div className="mb-4 flex justify-center">
                  <div className="p-3 rounded-full bg-blue-50 group-hover:scale-110 transition-transform duration-200">
                    {feature.icon}
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl sm:text-4xl font-bold text-gray-900 mb-2">
                  {stat.value}
                </div>
                <div className="text-gray-600 text-sm sm:text-base">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Make a Difference?
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Join thousands of citizens who are already using our platform to create
              positive change in their communities.
            </p>
            <Link to="/feedback">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200">
                Get Started Today
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;